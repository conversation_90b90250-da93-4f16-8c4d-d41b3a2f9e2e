<!DOCTYPE html>
<html>
<head>
    <title>Test Filter Button</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.5.2/bootbox.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Đả<PERSON> bảo nút Add new filter vẫn có thể click được khi có class disabled */
        .open_form.disabled {
            pointer-events: auto !important;
            cursor: pointer !important;
        }

        /* Override Bootstrap disabled button styles */
        .btn.disabled.open_form,
        .btn[disabled].open_form,
        fieldset[disabled] .btn.open_form {
            pointer-events: auto !important;
            cursor: pointer !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Filter Button</h1>
        
        <div class="alert alert-info">
            Bạn đã đạt giới hạn số lượng bộ lọc. Vui lòng nâng cấp VIP để tạo thêm bộ lọc.
        </div>
        
        <div class="margin-bottom-lg">
            <a href="#" title="Add new filter" class="btn btn-primary open_form disabled">Add new filter (Disabled)</a>
            <a href="#" title="Add new filter" class="btn btn-primary open_form">Add new filter (Normal)</a>
        </div>
        
        <div class="panel panel-default" id="add_filter" style="display:none">
            <div class="panel-heading">Add Filter</div>
            <div class="panel-body">
                <p>Form thêm filter sẽ hiển thị ở đây...</p>
            </div>
        </div>
    </div>

    <script>
        function add_filter_show(){
            $('#add_filter').css("display", "block");
            $('html, body').animate({
              scrollTop: $("#add_filter").offset().top
            }, 800);
        }

        $(function() {
            $('.open_form').click(function(e) {
                e.preventDefault();
                // Kiểm tra nếu nút bị disabled thì hiển thị thông báo
                if ($(this).hasClass('disabled')) {
                    // Tìm thông báo lỗi từ alert trên trang
                    var alertMessage = $('.alert-info').text().trim();
                    var message = alertMessage || 'Bạn đã đạt giới hạn số lượng bộ lọc. Vui lòng nâng cấp VIP để tạo thêm bộ lọc.';
                    
                    // Sử dụng bootbox nếu có, nếu không thì dùng alert thông thường
                    if (typeof bootbox !== 'undefined') {
                        bootbox.alert(message);
                    } else {
                        alert(message);
                    }
                    return false;
                }
                add_filter_show();
            });
        });
    </script>
</body>
</html>
